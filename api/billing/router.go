package billing

import (
	"github.com/gin-gonic/gin"
)

// RegisterRoutes 注册计费相关路由
func RegisterRoutes(r *gin.RouterGroup) {
	// 计费相关路由
	billing := r.Group("/billing")
	{
		// Key相关接口
		// 获取Key概览统计
		billing.GET("/keys/overview/stats", GetKeyOverviewStats)

		// 获取Key状态
		billing.GET("/keys/:key/status", GetKeyStatus)

		// 获取用量历史
		billing.GET("/keys/:key/usage", GetKeyUsageHistory)

		// 获取用量统计
		billing.GET("/keys/:key/stats", GetKeyUsageStats)

		// 充值管理接口
		InitRechargeAPI(billing)

		// 资源包管理接口
		InitQuotaPackageAPI(billing)

		// 价格规则接口
		InitPriceRuleAPI(billing)

		// 初始化API密钥管理接口
		InitKeyCurdAPI(billing)
	}
}
