import { extendCurdApi, http, useCurdApi } from '@uozi-admin/request'

// 充值记录接口
export interface RechargeRecord {
  id: string
  user_id: string
  user?: {
    id: string
    name: string
    email: string
    avatar?: string
  }
  amount: number
  type: string
  status: string
  trade_no?: string
  description?: string
  operator_id?: string
  operator?: {
    id: string
    name: string
    email: string
  }
  created_at: string
  updated_at: string
}

// 创建充值请求接口
export interface CreateRechargeRequest {
  user_id: string
  amount: number
  type?: string
  description?: string
}

export const rechargeApi = extendCurdApi(useCurdApi<RechargeRecord>('/admin/billing/recharge_records'), {
  recharge: async (data: CreateRechargeRequest) => {
    return await http.post('/admin/billing/recharge', data)
  },
})
