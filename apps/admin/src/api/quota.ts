import type { ModelBase } from './http'
import { extendCurdApi, http, useCurdApi } from '@uozi-admin/request'

// 配额信息
export interface QuotaPackage extends ModelBase {
  user_id: string
  user?: {
    id: string
    name: string
    email: string
    avatar?: string
  }
  api_key?: string
  module: 'llm' | 'tts' | 'asr'
  model_name?: string
  quota: number
  used: number
  available: number
  expires_at?: number
  status: string
  type: string
  description?: string
  operator_id?: string
  operator?: {
    id: string
    name: string
    email: string
  }
}

// 创建资源包请求接口（与后端保持一致）
export interface CreateQuotaPackageRequest {
  user_id: string
  api_key?: string
  module: string
  model_name?: string
  quota: number
  expires_at?: number
  type?: string
  description?: string
}

// 更新资源包请求接口（与后端保持一致）
export interface UpdateQuotaPackageRequest {
  quota?: number
  expires_at?: number
  status?: string
  description?: string
}

export const quotaApi = extendCurdApi(useCurdApi<QuotaPackage>('/admin/billing/quota_packages'), {
  // 创建资源包
  createItem: async (data: CreateQuotaPackageRequest) => {
    return await http.post('/admin/billing/quota_packages', data)
  },
  // 更新资源包
  updateItem: async (id: string, data: UpdateQuotaPackageRequest) => {
    return await http.post(`/admin/billing/quota_packages/${id}`, data)
  },
  // 禁用资源包
  disableItem: async (id: string) => {
    return await http.put(`/admin/billing/quota_packages/${id}/disable`)
  },
  // 启用资源包
  enableItem: async (id: string) => {
    return await http.put(`/admin/billing/quota_packages/${id}/enable`)
  },
  // 获取用户资源包列表
  getUserQuotaPackages: async (userId: string) => {
    return await http.get(`/admin/billing/quota_packages?user_id=${userId}`)
  },
})
