package billing

import (
	"net/http"
	"strconv"

	"git.uozi.org/uozi/potato-billing-api/internal/billing"
	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/query"
	"git.uozi.org/uozi/potato-billing-api/types"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
	"gorm.io/gorm"
)

// CreateRechargeRequest 创建充值记录请求
type CreateRechargeRequest struct {
	UserID      uint64  `json:"user_id,string" binding:"required"`
	Amount      float64 `json:"amount" binding:"required,min=0.01"`
	Type        string  `json:"type,omitempty"`
	Description string  `json:"description,omitempty"`
}

// InitRechargeAPI 初始化充值API
func InitRechargeAPI(g *gin.RouterGroup) {
	// 初始化CRUD API
	cosyAPI := cosy.Api[model.RechargeRecord]("recharge_records")

	// 列表查询钩子
	cosyAPI.GetListHook(func(c *cosy.Ctx[model.RechargeRecord]) {
		c.SetTransformer(func(m *model.RechargeRecord) any {
			return m
		})
	})

	cosyAPI.InitRouter(g)

	// 自定义路由
	g.POST("/recharge", CreateRecharge)
	g.POST("/recharge/:id/confirm", ConfirmRecharge)
	g.POST("/recharge/:id/cancel", CancelRecharge)
}

// CreateRecharge 创建充值记录（管理员充值）
func CreateRecharge(c *gin.Context) {
	var req CreateRechargeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 获取操作员信息（假设从JWT中获取）
	operatorID := uint64(1) // TODO: 从JWT或session中获取当前用户ID

	// 检查用户是否存在
	user, err := query.User.Where(query.User.ID.Eq(req.UserID)).First()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 开启事务
	err = cosy.UseDB(c).Transaction(func(tx *gorm.DB) error {
		// 创建充值记录
		rechargeRecord := &model.RechargeRecord{
			UserID:      req.UserID,
			Amount:      req.Amount,
			Type:        req.Type,
			Status:      "completed", // 管理员充值直接完成
			Description: req.Description,
			OperatorID:  operatorID,
		}

		if err := tx.Create(rechargeRecord).Error; err != nil {
			return err
		}

		// 增加用户余额
		err := tx.Model(user).
			Update("balance", gorm.Expr("balance + ?", req.Amount)).
			Error
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		logger.Error("Failed to create recharge record", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	// 充值成功后，检查并恢复被阻塞的Key状态
	billingService := billing.GetBillingService()
	if billingService != nil {
		keyService := billingService.GetKeyService()
		if keyService != nil {
			err := keyService.CheckAndRestoreKeyStatus(c.Request.Context(), req.UserID, "", "")
			if err != nil {
				logger.Error("Failed to restore key status after recharge", "error", err, "userID", req.UserID)
				// 不影响充值成功的响应，只记录错误
			} else {
				logger.Info("Successfully checked and restored key status after recharge", "userID", req.UserID)
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "充值成功",
		"amount":  req.Amount,
	})
}

// ConfirmRecharge 确认充值（用于处理第三方支付回调）
func ConfirmRecharge(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 查询充值记录
	rechargeRecord, err := query.RechargeRecord.
		Preload(query.RechargeRecord.User).
		Where(query.RechargeRecord.ID.Eq(id)).
		First()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	if rechargeRecord.Status != types.RechargeStatusPending {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "充值记录状态不允许确认",
		})
		return
	}

	// 开启事务
	err = cosy.UseDB(c).Transaction(func(tx *gorm.DB) error {
		// 更新充值记录状态
		err := tx.Model(rechargeRecord).Update("status", types.RechargeStatusCompleted).Error
		if err != nil {
			return err
		}

		// 增加用户余额
		err = tx.Model(rechargeRecord.User).Update("balance", gorm.Expr("balance + ?", rechargeRecord.Amount)).Error
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		logger.Error("Failed to confirm recharge", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	// 充值确认成功后，检查并恢复被阻塞的Key状态
	billingService := billing.GetBillingService()
	if billingService != nil {
		keyService := billingService.GetKeyService()
		if keyService != nil {
			err := keyService.CheckAndRestoreKeyStatus(c.Request.Context(), rechargeRecord.UserID, "", "")
			if err != nil {
				logger.Error("Failed to restore key status after recharge confirmation", "error", err, "userID", rechargeRecord.UserID)
				// 不影响充值确认成功的响应，只记录错误
			} else {
				logger.Info("Successfully checked and restored key status after recharge confirmation", "userID", rechargeRecord.UserID)
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "充值确认成功",
	})
}

// CancelRecharge 取消充值
func CancelRecharge(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 查询充值记录
	rechargeRecord, err := query.RechargeRecord.
		Where(query.RechargeRecord.ID.Eq(id)).
		First()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	if rechargeRecord.Status != types.RechargeStatusPending {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "只能取消待处理的充值记录",
		})
		return
	}

	// 更新状态
	_, err = query.RechargeRecord.
		Where(query.RechargeRecord.ID.Eq(id)).
		Update(query.RechargeRecord.Status, types.RechargeStatusCancelled)
	if err != nil {
		logger.Error("Failed to cancel recharge", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "充值已取消",
	})
}
