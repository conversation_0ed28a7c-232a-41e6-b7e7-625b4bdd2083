package billing

import (
	"net/http"
	"strconv"
	"time"

	"git.uozi.org/uozi/potato-billing-api/api"
	"git.uozi.org/uozi/potato-billing-api/internal/billing"
	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/query"
	"git.uozi.org/uozi/potato-billing-api/types"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
)

// CreateQuotaPackageRequest 创建资源包请求
type CreateQuotaPackageRequest struct {
	UserID      uint64 `json:"user_id,string" binding:"required"`
	APIKey      string `json:"api_key,omitempty"` // 可选，如果指定则只对该Key生效
	Module      string `json:"module" binding:"required"`
	ModelName   string `json:"model_name,omitempty"`
	Quota       int64  `json:"quota" binding:"required,min=1"`
	ExpiresAt   *int64 `json:"expires_at,omitempty"`
	Type        string `json:"type,omitempty"`
	Description string `json:"description,omitempty"`
}

// UpdateQuotaPackageRequest 更新资源包请求
type UpdateQuotaPackageRequest struct {
	Quota       *int64 `json:"quota,omitempty" binding:"omitempty,min=1"`
	ExpiresAt   *int64 `json:"expires_at,omitempty"`
	Status      string `json:"status,omitempty"`
	Description string `json:"description,omitempty"`
}

// InitQuotaPackageAPI 初始化资源包API
func InitQuotaPackageAPI(g *gin.RouterGroup) {
	g.GET("/quota_packages", GetQuotaPackageList)
	g.POST("/quota_packages", CreateQuotaPackage)
	g.POST("/quota_packages/:id", UpdateQuotaPackage)
	g.GET("/quota_packages/user/:user_id", GetUserQuotaPackages)
	g.PUT("/quota_packages/:id/enable", EnableQuotaPackage)
	g.PUT("/quota_packages/:id/disable", DisableQuotaPackage)
}

// GetQuotaPackageList 获取资源包列表
func GetQuotaPackageList(c *gin.Context) {
	cosy.Core[model.QuotaPackageRecord](c).
		SetPreloads("User", "Operator").
		PagingList()
}

// CreateQuotaPackage 创建资源包
func CreateQuotaPackage(c *gin.Context) {
	var req CreateQuotaPackageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 获取操作员信息
	operatorID := api.CurrentUser(c).ID

	// 创建资源包记录
	quotaPackage := &model.QuotaPackageRecord{
		UserID:      req.UserID,
		APIKey:      req.APIKey,
		Module:      req.Module,
		ModelName:   req.ModelName,
		Quota:       req.Quota,
		Used:        0,
		ExpiresAt:   0,
		Status:      types.QuotaPackageStatusActive,
		Type:        req.Type,
		Description: req.Description,
		OperatorID:  operatorID,
	}

	if req.ExpiresAt != nil {
		quotaPackage.ExpiresAt = *req.ExpiresAt
	}

	if req.Type == "" {
		quotaPackage.Type = types.QuotaPackageSourceAdmin
	}

	err := cosy.UseDB(c).Create(quotaPackage).Error
	if err != nil {
		logger.Error("Failed to create quota package", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	// 资源包创建成功后，检查并恢复相关Key状态
	billingService := billing.GetBillingService()
	if billingService != nil {
		keyService := billingService.GetKeyService()
		if keyService != nil {
			err := keyService.CheckAndRestoreKeyStatus(c.Request.Context(), req.UserID, req.APIKey, req.Module)
			if err != nil {
				logger.Error("Failed to restore key status after quota package creation", "error", err,
					"userID", req.UserID, "apiKey", req.APIKey, "module", req.Module)
				// 不影响资源包创建成功的响应，只记录错误
			} else {
				logger.Info("Successfully checked and restored key status after quota package creation",
					"userID", req.UserID, "apiKey", req.APIKey, "module", req.Module)
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "资源包创建成功",
		"data":    quotaPackage,
	})
}

// UpdateQuotaPackage 更新资源包
func UpdateQuotaPackage(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	var req UpdateQuotaPackageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	updates := make(map[string]interface{})

	if req.Quota != nil {
		updates["quota"] = *req.Quota
	}
	if req.ExpiresAt != nil {
		updates["expires_at"] = *req.ExpiresAt
	}
	if req.Status != "" {
		updates["status"] = req.Status
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}

	err = cosy.UseDB(c).Model(&model.QuotaPackageRecord{}).
		Where("id = ?", id).
		Updates(updates).Error
	if err != nil {
		logger.Error("Failed to update quota package", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "资源包更新成功",
	})
}

// DisableQuotaPackage 禁用资源包
func DisableQuotaPackage(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	err = cosy.UseDB(c).Model(&model.QuotaPackageRecord{}).
		Where("id = ?", id).
		Update("status", types.QuotaPackageStatusDisabled).Error
	if err != nil {
		logger.Error("Failed to disable quota package", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "资源包已禁用",
	})
}

// EnableQuotaPackage 启用资源包
func EnableQuotaPackage(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 检查资源包是否过期
	var quotaPackage model.QuotaPackageRecord
	err = cosy.UseDB(c).Where("id = ?", id).First(&quotaPackage).Error
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	status := types.QuotaPackageStatusActive
	if quotaPackage.ExpiresAt > 0 && quotaPackage.ExpiresAt < time.Now().UnixMilli() {
		status = types.QuotaPackageStatusExpired
	} else if quotaPackage.Used >= quotaPackage.Quota {
		status = types.QuotaPackageStatusExhausted
	}

	err = cosy.UseDB(c).Model(&model.QuotaPackageRecord{}).
		Where("id = ?", id).
		Update("status", status).Error
	if err != nil {
		logger.Error("Failed to enable quota package", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	// 如果资源包被成功启用，检查并恢复相关Key状态
	if status == types.QuotaPackageStatusActive {
		billingService := billing.GetBillingService()
		if billingService != nil {
			keyService := billingService.GetKeyService()
			if keyService != nil {
				err := keyService.CheckAndRestoreKeyStatus(c.Request.Context(), quotaPackage.UserID, quotaPackage.APIKey, quotaPackage.Module)
				if err != nil {
					logger.Error("Failed to restore key status after quota package enable", "error", err,
						"userID", quotaPackage.UserID, "apiKey", quotaPackage.APIKey, "module", quotaPackage.Module)
					// 不影响启用成功的响应，只记录错误
				} else {
					logger.Info("Successfully checked and restored key status after quota package enable",
						"userID", quotaPackage.UserID, "apiKey", quotaPackage.APIKey, "module", quotaPackage.Module)
				}
			}
		}
	}

	message := "资源包已启用"
	if status != "active" {
		message = "资源包状态已更新为" + status
	}

	c.JSON(http.StatusOK, gin.H{
		"message": message,
		"status":  status,
	})
}

// GetUserQuotaPackages 获取用户资源包列表
func GetUserQuotaPackages(c *gin.Context) {
	userID := cast.ToUint64(c.Param("user_id"))

	// 获取查询参数
	module := c.Query("module")
	status := c.Query("status")
	apiKey := c.Query("api_key")

	q := query.QuotaPackageRecord.Where(query.QuotaPackageRecord.UserID.Eq(userID))

	if module != "" {
		q = q.Where(query.QuotaPackageRecord.Module.Eq(module))
	}
	if status != "" {
		q = q.Where(query.QuotaPackageRecord.Status.Eq(status))
	}
	if apiKey != "" {
		q = q.Where(query.QuotaPackageRecord.APIKey.Eq(apiKey))
	}

	quotaPackages, err := q.
		Preload(query.QuotaPackageRecord.User).
		Preload(query.QuotaPackageRecord.Operator).
		Order(query.QuotaPackageRecord.CreatedAt.Desc()).
		Find()
	if err != nil {
		logger.Error("Failed to get user quota packages", "error", err)
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":  quotaPackages,
		"total": len(quotaPackages),
	})
}
